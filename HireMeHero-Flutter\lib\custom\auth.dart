import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hiremehero/custom/util.dart';
import 'package:hiremehero/main.dart';
import 'package:hiremehero/pages/main_tabs/home.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:crypto/crypto.dart';

class Auth {
  static Future<void> nativeGoogleSignIn() async {
    const webClientId =
        '************-aghhu8a60rrv7kncpsqvfa1busink3ec.apps.googleusercontent.com';
    const iosClientId =
        '************-r5vc4b8am4fjug4o6fdbqfhrvojdvktq.apps.googleusercontent.com';

    final GoogleSignIn googleSignIn = GoogleSignIn(
      clientId: iosClientId,
      serverClientId: webClientId,
      scopes: ['email', 'profile'],

    );

    // Clear any existing sign in state
    await googleSignIn.signOut();

    // Show the account picker
    final googleUser = await googleSignIn.signIn();
    if (googleUser == null) {
      debugPrint('Google Sign In was cancelled by user');
      return;
    }

    // Get authentication details
    final googleAuth = await googleUser.authentication;
    final accessToken = googleAuth.accessToken;
    final idToken = googleAuth.idToken;

    if (accessToken == null) {
      throw 'No Access Token found.';
    }
    if (idToken == null) {
      throw 'No ID Token found.';
    }

    final res = await supabase.auth.signInWithIdToken(
      provider: OAuthProvider.google,
      idToken: idToken,
      accessToken: accessToken,
    );

    if (res.user != null) {
      NavigationService.navigatorKey.currentState?.pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const TabbedHomePage()),
        (route) => false,
      );
    }
  }

  static Future<void> oathGoogleSignIn() async {
    await supabase.auth.signInWithOAuth(
      OAuthProvider.google,
      redirectTo: kIsWeb
          ? null
          : 'hiremehero://login-callback', // set the redirect link to bring back the user via deeplink.
      authScreenLaunchMode: kIsWeb
          ? LaunchMode.platformDefault
          : LaunchMode
              .externalApplication, // Launch the auth screen in a new webview on mobile.
    );
  }

  static Future<AuthResponse> signInWithApple() async {
    final rawNonce = supabase.auth.generateRawNonce();
    final hashedNonce = sha256.convert(utf8.encode(rawNonce)).toString();

    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
      nonce: hashedNonce,
    );

    final idToken = credential.identityToken;
    if (idToken == null) {
      throw const AuthException(
          'Could not find ID Token from generated credential.');
    }
    return supabase.auth.signInWithIdToken(
      provider: OAuthProvider.apple,
      idToken: idToken,
      nonce: rawNonce,
    );
  }
}
